# EKS Terragrunt Multi-Environment Setup

This repository contains a complete Terragrunt configuration for deploying AWS EKS clusters across multiple environments (development, staging, and production) with ArgoCD for GitOps. Terragrunt is used to keep the infrastructure code DRY (Don't Repeat Yourself) and manage multiple environments efficiently.

## 🏗️ Architecture

The infrastructure includes:

- **Multi-environment setup**: DRY configuration using Terragrunt for dev, staging, and production
- **VPC with proper networking**: Public/private subnets, NAT gateways, security groups
- **EKS cluster**: With proper IAM roles, encryption, and logging
- **EKS node groups**: Auto-scaling with different instance types per environment
- **ArgoCD v2.13.1**: Latest version installed via Helm chart 8.2.7 with RBAC and IRSA support
- **AWS Load Balancer Controller**: For ingress management
- **Security best practices**: Encryption at rest, IRSA, proper security groups

## 📁 Directory Structure

```
├── modules/
│   ├── vpc/                    # VPC module with networking
│   ├── eks-cluster/            # EKS cluster module
│   ├── eks-node-groups/        # EKS node groups module
│   ├── aws-load-balancer-controller/ # AWS Load Balancer Controller module
│   └── argocd/                 # ArgoCD installation module
├── environments/
│   ├── dev/
│   │   ├── terragrunt.hcl      # Development environment configuration
│   │   └── terraform.tfvars    # Development-specific variables
│   ├── staging/
│   │   ├── terragrunt.hcl      # Staging environment configuration
│   │   └── terraform.tfvars    # Staging-specific variables
│   └── prod/
│       ├── terragrunt.hcl      # Production environment configuration
│       └── terraform.tfvars    # Production-specific variables
├── terragrunt.hcl              # Root Terragrunt configuration
├── common.hcl                  # Common configuration shared across environments
├── main.tf                     # Root Terraform configuration
├── variables.tf                # Root variables
├── Makefile                    # Convenient commands for Terragrunt operations
└── README.md                   # This file
```

## 🚀 Quick Start

### Prerequisites

1. **AWS CLI** configured with appropriate permissions
2. **Terraform** >= 1.0 installed
3. **Terragrunt** >= 0.50.0 installed ([Installation Guide](https://terragrunt.gruntwork.io/docs/getting-started/install/))
4. **kubectl** installed for cluster management
5. **Helm** installed for ArgoCD management

### Required AWS Permissions

Your AWS credentials need the following permissions:
- EC2 (VPC, subnets, security groups, NAT gateways)
- EKS (cluster and node group management)
- IAM (roles and policies for EKS and IRSA)
- CloudWatch (log groups)
- KMS (encryption keys)
- S3 (for Terraform state storage)
- DynamoDB (for state locking)

### Backend Configuration

Terragrunt automatically manages the S3 backend configuration. The backend is configured in the root `terragrunt.hcl` file and uses:

- **S3 Bucket**: `eks-terraform-state-{account-id}` (automatically created)
- **DynamoDB Table**: `eks-terraform-state-locks` (automatically created)
- **State Path**: `environments/{environment}/terraform.tfstate`

You can customize the bucket prefix by setting the `TG_BUCKET_PREFIX` environment variable:

```bash
export TG_BUCKET_PREFIX="my-custom-prefix"
```

## 🌍 Environment Deployment

### Using Terragrunt Commands

#### Development Environment

```bash
# Initialize and apply development environment
make dev-init
make dev-plan
make dev-apply

# Or using Terragrunt directly
cd environments/dev
terragrunt init
terragrunt plan
terragrunt apply
```

#### Staging Environment

```bash
# Initialize and apply staging environment
make staging-init
make staging-plan
make staging-apply

# Or using Terragrunt directly
cd environments/staging
terragrunt init
terragrunt plan
terragrunt apply
```

#### Production Environment

```bash
# Initialize and apply production environment
make prod-init
make prod-plan
make prod-apply

# Or using Terragrunt directly
cd environments/prod
terragrunt init
terragrunt plan
terragrunt apply
```

#### Deploy All Environments

```bash
# Initialize all environments
make run-all-init

# Plan all environments
make run-all-plan

# Apply all environments (use with caution!)
make run-all-apply
```

## ⚙️ Configuration

### Environment-Specific Settings

| Setting | Development | Staging | Production |
|---------|-------------|---------|------------|
| AWS Region | ap-southeast-1 | us-west-2 | us-west-2 |
| VPC CIDR | 10.0.0.0/16 | 10.1.0.0/16 | 10.2.0.0/16 |
| Instance Types | t3.medium | t3.large | m5.xlarge, c5.2xlarge |
| Node Count | 2-3 | 3-6 | 5-10 |
| NAT Gateway | Single | Multi-AZ | Multi-AZ |
| Log Retention | 3 days | 14 days | 30 days |
| Public Access | Open | Restricted | Private only |
| ArgoCD Access | ALB + HTTP | LoadBalancer Internal | LoadBalancer Internal |

### Key Features

- **DRY Configuration**: Common settings in `common.hcl`, environment-specific overrides in `terragrunt.hcl`
- **Latest ArgoCD**: Version 2.13.1 with Helm chart 8.2.7
- **Automatic Backend**: S3 backend and DynamoDB table automatically managed
- **Environment Isolation**: Separate state files and resources per environment
- **Cost Optimization**: Spot instances and smaller resources for dev/staging

### Key Variables

- `cluster_version`: Kubernetes version (default: "1.31")
- `node_groups`: Map of node group configurations
- `vpc_cidr`: VPC CIDR block (different per environment)
- `argocd_chart_version`: ArgoCD Helm chart version (default: "8.2.7")
- `argocd_version`: ArgoCD application version (default: "v2.13.1")

## 🔐 Security Features

- **Encryption at rest**: EKS secrets encrypted with KMS
- **IRSA**: IAM Roles for Service Accounts for ArgoCD
- **Network security**: Proper security groups and NACLs
- **Private subnets**: Worker nodes in private subnets
- **Restricted access**: Production clusters with limited public access

## 📊 Accessing Your Cluster

### Configure kubectl

```bash
# Using Makefile (automatically detects cluster name and region)
make kubectl-config ENV=dev

# Or manually
aws eks update-kubeconfig --region ap-southeast-1 --name eks-demo-dev
```

### Access ArgoCD

1. **Get admin password**:
```bash
# Using Makefile
make argocd-password ENV=dev

# Or manually
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d
```

2. **Port forward** (for development with ALB):
```bash
# Using Makefile
make argocd-port-forward

# Or manually
kubectl port-forward svc/argocd-server -n argocd 8080:443
```

3. **Access via LoadBalancer** (staging/production):
```bash
kubectl get svc argocd-server -n argocd
```

4. **Access via ALB** (development):
```bash
kubectl get ingress -n argocd
```

## 🔧 Customization

### Terragrunt Configuration

Terragrunt allows you to customize configurations at different levels:

1. **Root level** (`terragrunt.hcl`): Backend and provider configuration
2. **Common level** (`common.hcl`): Shared defaults across all environments
3. **Environment level** (`environments/{env}/terragrunt.hcl`): Environment-specific overrides

### Adding Node Groups

Modify the `node_groups` input in your environment's `terragrunt.hcl`:

```hcl
inputs = {
  node_groups = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      desired_size   = 2
      max_size       = 4
      min_size       = 1
      disk_size      = 50
      labels = {
        role = "general"
      }
      taints = []
    }
    spot = {
      instance_types = ["t3.medium", "t3.large"]
      capacity_type  = "SPOT"
      desired_size   = 1
      max_size       = 3
      min_size       = 0
      disk_size      = 50
      labels = {
        role = "spot"
      }
      taints = [
        {
          key    = "spot"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }
}
```

### ArgoCD Configuration

Customize ArgoCD in your environment's `terragrunt.hcl`:

```hcl
inputs = {
  argocd_server_service_type = "LoadBalancer"  # or "ClusterIP"
  argocd_load_balancer_scheme = "internet-facing"  # or "internal"
  argocd_enable_alb_ingress = true  # for ALB ingress
  argocd_enable_insecure_mode = true  # required for ALB HTTP
}
```

### Common Configuration Updates

To update settings across all environments, modify `common.hcl`:

```hcl
locals {
  argocd_config = {
    chart_version = "8.2.7"  # Update ArgoCD chart version
    argocd_version = "v2.13.1"  # Update ArgoCD application version
  }
}
```

## 🧹 Cleanup

To destroy an environment:

```bash
# Using Makefile
make destroy ENV=dev

# Or using Terragrunt directly
cd environments/dev
terragrunt destroy
```

To clean up Terragrunt cache files:

```bash
# Clean specific environment
make clean ENV=dev

# Or manually
cd environments/dev
rm -rf .terraform .terraform.lock.hcl .terragrunt-cache backend.tf provider.tf
```

## 📝 Notes

- **DRY Principle**: Terragrunt eliminates code duplication across environments
- **State Management**: Each environment has its own S3 state file with DynamoDB locking
- **Module Reusability**: All modules are reusable across environments via Terragrunt
- **Security**: Production uses internal load balancers and restricted access
- **Scaling**: Node groups support auto-scaling based on demand
- **Monitoring**: CloudWatch logging enabled for all clusters
- **Latest Versions**: ArgoCD 2.13.1 with Helm chart 8.2.7
- **Backend Management**: S3 bucket and DynamoDB table automatically created and managed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test in development environment
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
