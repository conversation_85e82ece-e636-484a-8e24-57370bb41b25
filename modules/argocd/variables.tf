variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for ArgoCD"
  type        = string
  default     = "argocd"
}

variable "create_namespace" {
  description = "Whether to create the namespace"
  type        = bool
  default     = true
}

variable "release_name" {
  description = "<PERSON><PERSON> release name for ArgoCD"
  type        = string
  default     = "argocd"
}

variable "chart_version" {
  description = "Version of the ArgoCD Helm chart"
  type        = string
  default     = "8.2.7"
}

variable "argocd_version" {
  description = "Version of ArgoCD"
  type        = string
  default     = "v3.0.12"
}

variable "server_service_type" {
  description = "Service type for ArgoCD server (ClusterIP, NodePort, LoadBalancer)"
  type        = string
  default     = "LoadBalancer"
}

variable "load_balancer_scheme" {
  description = "Load balancer scheme (internal or internet-facing)"
  type        = string
  default     = "internet-facing"
}

variable "enable_ingress" {
  description = "Enable ingress for ArgoCD server"
  type        = bool
  default     = false
}

variable "ingress_class_name" {
  description = "Ingress class name"
  type        = string
  default     = null
}

variable "ingress_hosts" {
  description = "List of ingress hosts"
  type        = list(string)
  default     = []
}

variable "enable_alb_ingress" {
  description = "Enable ALB ingress for ArgoCD server (alternative to LoadBalancer)"
  type        = bool
  default     = false
}

variable "alb_ingress_name" {
  description = "Name for the ALB ingress resource"
  type        = string
  default     = "argocd-alb-ingress"
}

variable "alb_load_balancer_name" {
  description = "Name for the ALB load balancer"
  type        = string
  default     = "argocd-alb"
}

variable "enable_insecure_mode" {
  description = "Enable ArgoCD server insecure mode (required for ALB HTTP)"
  type        = bool
  default     = false
}

variable "rbac_default_policy" {
  description = "Default RBAC policy"
  type        = string
  default     = "role:readonly"
}

variable "repo_server_replicas" {
  description = "Number of repository server replicas"
  type        = number
  default     = 1
}

variable "controller_replicas" {
  description = "Number of application controller replicas"
  type        = number
  default     = 1
}

variable "enable_redis" {
  description = "Enable Redis for ArgoCD"
  type        = bool
  default     = true
}

variable "enable_dex" {
  description = "Enable Dex for SSO"
  type        = bool
  default     = false
}

variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts (IRSA)"
  type        = bool
  default     = true
}

variable "service_account_name" {
  description = "Name of the service account for ArgoCD"
  type        = string
  default     = "argocd-server"
}

variable "oidc_provider_arn" {
  description = "ARN of the OIDC provider for IRSA"
  type        = string
  default     = null
}

variable "oidc_issuer_url" {
  description = "URL of the OIDC issuer"
  type        = string
  default     = null
}

variable "additional_helm_values" {
  description = "Additional Helm values as YAML string"
  type        = string
  default     = null
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
