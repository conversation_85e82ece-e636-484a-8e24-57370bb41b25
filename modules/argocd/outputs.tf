output "namespace" {
  description = "Kubernetes namespace where ArgoCD is deployed"
  value       = var.create_namespace ? kubernetes_namespace.argocd[0].metadata[0].name : var.namespace
}

output "release_name" {
  description = "Helm release name for ArgoCD"
  value       = helm_release.argocd.name
}

output "chart_version" {
  description = "Version of the ArgoCD Helm chart"
  value       = helm_release.argocd.version
}

output "argocd_server_service_name" {
  description = "Name of the ArgoCD server service"
  value       = "${var.release_name}-server"
}

output "argocd_admin_password" {
  description = "ArgoCD admin password (base64 encoded)"
  value       = try(data.kubernetes_secret.argocd_initial_admin_secret.data["password"], "")
  sensitive   = true
}

output "argocd_server_url" {
  description = "ArgoCD server URL (when using LoadBalancer)"
  value       = var.server_service_type == "LoadBalancer" ? "https://${helm_release.argocd.name}-server.${var.create_namespace ? kubernetes_namespace.argocd[0].metadata[0].name : var.namespace}.svc.cluster.local" : null
}

output "iam_role_arn" {
  description = "ARN of the IAM role for ArgoCD service account"
  value       = var.enable_irsa ? aws_iam_role.argocd[0].arn : null
}

output "iam_role_name" {
  description = "Name of the IAM role for ArgoCD service account"
  value       = var.enable_irsa ? aws_iam_role.argocd[0].name : null
}

output "service_account_name" {
  description = "Name of the Kubernetes service account for ArgoCD"
  value       = var.enable_irsa ? kubernetes_service_account.argocd[0].metadata[0].name : null
}

output "helm_values" {
  description = "Helm values used for ArgoCD deployment"
  value       = helm_release.argocd.values
  sensitive   = true
}

output "alb_ingress_hostname" {
  description = "ALB ingress hostname for ArgoCD (when ALB ingress is enabled)"
  value       = var.enable_alb_ingress ? try(kubernetes_ingress_v1.argocd_alb[0].status[0].load_balancer[0].ingress[0].hostname, "") : null
}

output "alb_ingress_name" {
  description = "Name of the ALB ingress resource"
  value       = var.enable_alb_ingress ? kubernetes_ingress_v1.argocd_alb[0].metadata[0].name : null
}

output "access_url" {
  description = "URL to access ArgoCD (ALB hostname or LoadBalancer service)"
  value = var.enable_alb_ingress ? (
    try("http://${kubernetes_ingress_v1.argocd_alb[0].status[0].load_balancer[0].ingress[0].hostname}", "ALB hostname not yet available")
    ) : (
    var.server_service_type == "LoadBalancer" ? "Check LoadBalancer service for external IP" : "Service not externally accessible"
  )
}
