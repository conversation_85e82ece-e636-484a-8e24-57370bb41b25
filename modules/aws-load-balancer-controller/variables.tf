variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "oidc_provider_arn" {
  description = "ARN of the OIDC provider for the EKS cluster"
  type        = string
}

variable "oidc_issuer_url" {
  description = "URL of the OIDC issuer for the EKS cluster"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID where the EKS cluster is deployed"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace for AWS Load Balancer Controller"
  type        = string
  default     = "kube-system"
}

variable "create_namespace" {
  description = "Whether to create the namespace"
  type        = bool
  default     = false
}

variable "service_account_name" {
  description = "Name of the Kubernetes service account"
  type        = string
  default     = "aws-load-balancer-controller"
}

variable "release_name" {
  description = "Name of the Helm release"
  type        = string
  default     = "aws-load-balancer-controller"
}

variable "chart_version" {
  description = "Version of the AWS Load Balancer Controller Helm chart"
  type        = string
  default     = "1.6.2"
}

variable "image_repository" {
  description = "Docker image repository for AWS Load Balancer Controller"
  type        = string
  default     = "************.dkr.ecr.us-west-2.amazonaws.com/amazon/aws-load-balancer-controller"
}

variable "image_tag" {
  description = "Docker image tag for AWS Load Balancer Controller"
  type        = string
  default     = "v2.6.2"
}

variable "replica_count" {
  description = "Number of replicas for the controller"
  type        = number
  default     = 2
}

variable "additional_helm_values" {
  description = "Additional Helm values to pass to the chart"
  type        = map(string)
  default     = {}
}

variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
