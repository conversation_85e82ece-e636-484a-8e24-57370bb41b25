# Main Terraform configuration for EKS infrastructure
# This file is used by all environments through Terragrunt

terraform {
  required_version = ">= 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.11"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }
}

# Local values
locals {
  cluster_name = "${var.project_name}-${var.environment}"

  common_tags = merge(var.tags, {
    Environment = var.environment
    Project     = var.project_name
    ManagedBy   = "terragrunt"
    Owner       = var.owner
  })
}

# VPC Module
module "vpc" {
  source = "./modules/vpc"

  cluster_name         = local.cluster_name
  vpc_cidr             = var.vpc_cidr
  public_subnet_cidrs  = var.public_subnet_cidrs
  private_subnet_cidrs = var.private_subnet_cidrs
  enable_nat_gateway   = var.enable_nat_gateway
  single_nat_gateway   = var.single_nat_gateway

  tags = local.common_tags
}

# EKS Cluster Module
module "eks_cluster" {
  source = "./modules/eks-cluster"

  cluster_name    = local.cluster_name
  cluster_version = var.cluster_version

  private_subnet_ids                   = module.vpc.private_subnet_ids
  public_subnet_ids                    = module.vpc.public_subnet_ids
  cluster_security_group_id            = module.vpc.cluster_security_group_id
  cluster_endpoint_private_access      = var.cluster_endpoint_private_access
  cluster_endpoint_public_access       = var.cluster_endpoint_public_access
  cluster_endpoint_public_access_cidrs = var.cluster_endpoint_public_access_cidrs
  cluster_enabled_log_types            = var.cluster_enabled_log_types
  cloudwatch_log_retention_in_days     = var.cloudwatch_log_retention_in_days

  # EKS Add-ons
  enable_vpc_cni_addon    = var.enable_vpc_cni_addon
  enable_coredns_addon    = var.enable_coredns_addon
  enable_kube_proxy_addon = var.enable_kube_proxy_addon
  enable_ebs_csi_addon    = var.enable_ebs_csi_addon

  tags = local.common_tags

  depends_on = [module.vpc]
}

# EKS Node Groups Module
module "eks_node_groups" {
  source = "./modules/eks-node-groups"

  for_each = var.node_groups

  cluster_name                         = module.eks_cluster.cluster_id
  node_group_name                      = each.key
  cluster_endpoint                     = module.eks_cluster.cluster_endpoint
  cluster_certificate_authority_data   = module.eks_cluster.cluster_certificate_authority_data
  cluster_version                      = var.cluster_version
  subnet_ids                           = module.vpc.private_subnet_ids
  node_security_group_id               = module.vpc.node_security_group_id

  instance_types                       = each.value.instance_types
  capacity_type                        = each.value.capacity_type
  desired_size                         = each.value.desired_size
  max_size                             = each.value.max_size
  min_size                             = each.value.min_size
  disk_size                            = each.value.disk_size

  tags = merge(local.common_tags, each.value.labels)

  depends_on = [module.eks_cluster]
}

# AWS Load Balancer Controller Module
module "aws_load_balancer_controller" {
  source = "./modules/aws-load-balancer-controller"

  cluster_name         = module.eks_cluster.cluster_id
  oidc_provider_arn    = module.eks_cluster.oidc_provider_arn
  oidc_issuer_url      = module.eks_cluster.cluster_oidc_issuer_url
  vpc_id               = module.vpc.vpc_id
  chart_version        = var.aws_load_balancer_controller_chart_version
  image_tag            = var.aws_load_balancer_controller_image_tag
  replica_count        = var.aws_load_balancer_controller_replica_count
  service_account_name = var.aws_load_balancer_controller_service_account_name
  release_name         = var.aws_load_balancer_controller_release_name

  tags = local.common_tags

  depends_on = [module.eks_node_groups]
}

# ArgoCD Module
module "argocd" {
  source = "./modules/argocd"

  cluster_name         = local.cluster_name
  namespace            = var.argocd_namespace
  create_namespace     = var.argocd_create_namespace
  chart_version        = var.argocd_chart_version
  argocd_version       = var.argocd_version
  server_service_type  = var.argocd_server_service_type
  load_balancer_scheme = var.argocd_load_balancer_scheme

  enable_irsa       = var.argocd_enable_irsa
  oidc_provider_arn = module.eks_cluster.oidc_provider_arn
  oidc_issuer_url   = module.eks_cluster.cluster_oidc_issuer_url

  enable_alb_ingress      = var.argocd_enable_alb_ingress
  alb_ingress_name        = var.argocd_alb_ingress_name
  alb_load_balancer_name  = var.argocd_alb_load_balancer_name
  enable_insecure_mode    = var.argocd_enable_insecure_mode

  tags = local.common_tags

  depends_on = [module.eks_node_groups]
}

# Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "cluster_id" {
  description = "EKS cluster ID"
  value       = module.eks_cluster.cluster_id
}

output "cluster_arn" {
  description = "EKS cluster ARN"
  value       = module.eks_cluster.cluster_arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks_cluster.cluster_endpoint
}

output "cluster_security_group_id" {
  description = "Security group ids attached to the cluster control plane"
  value       = module.eks_cluster.cluster_security_group_id
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = module.eks_cluster.cluster_oidc_issuer_url
}

output "argocd_admin_password" {
  description = "ArgoCD admin password"
  value       = module.argocd.argocd_admin_password
  sensitive   = true
}

output "argocd_access_url" {
  description = "URL to access ArgoCD"
  value       = module.argocd.access_url
}
