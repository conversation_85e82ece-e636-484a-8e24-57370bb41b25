# Staging Environment Terragrunt Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Include common configuration
include "common" {
  path = "${dirname(find_in_parent_folders())}/common.hcl"
}

# Terraform source configuration
terraform {
  source = "${dirname(find_in_parent_folders())}"
}

# Environment-specific inputs
inputs = {
  # Environment identification
  environment = "staging"
  aws_region  = "us-west-2"

  # VPC Configuration - Staging specific
  vpc_cidr             = "10.1.0.0/16"
  public_subnet_cidrs  = ["10.1.1.0/24", "10.1.2.0/24"]
  private_subnet_cidrs = ["10.1.10.0/24", "10.1.20.0/24"]
  single_nat_gateway   = false  # Multi-AZ for staging

  # EKS Cluster Configuration - Staging specific
  cluster_version                      = "1.31"
  cluster_endpoint_public_access_cidrs = ["10.0.0.0/8", "172.16.0.0/12"]  # Restricted access
  cloudwatch_log_retention_in_days     = 14

  # Node Groups Configuration - Staging specific
  node_groups = {
    general = {
      instance_types = ["t3.large"]
      capacity_type  = "ON_DEMAND"
      desired_size   = 3
      max_size       = 6
      min_size       = 2
      disk_size      = 50
      labels = {
        role        = "general"
        environment = "staging"
      }
      taints = []
    }
    spot = {
      instance_types = ["t3.large", "t3.xlarge"]
      capacity_type  = "SPOT"
      desired_size   = 1
      max_size       = 3
      min_size       = 0
      disk_size      = 50
      labels = {
        role        = "spot"
        environment = "staging"
      }
      taints = [
        {
          key    = "spot"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }

  # ArgoCD Configuration - Staging specific
  argocd_server_service_type  = "LoadBalancer"
  argocd_load_balancer_scheme = "internal"  # Internal access only

  # Staging-specific tags
  tags = {
    Environment = "staging"
    CostCenter  = "staging"
    Backup      = "daily"
  }
}
