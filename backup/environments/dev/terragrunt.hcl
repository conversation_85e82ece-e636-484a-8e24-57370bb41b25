# Development Environment Terragrunt Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Include common configuration
include "common" {
  path = "${dirname(find_in_parent_folders())}/common.hcl"
}

# Terraform source configuration
terraform {
  source = "${dirname(find_in_parent_folders())}"
}

# Environment-specific inputs
inputs = {
  # Environment identification
  environment = "dev"
  aws_region  = "ap-southeast-1"

  # VPC Configuration - Development specific
  vpc_cidr             = "10.0.0.0/16"
  public_subnet_cidrs  = ["10.0.1.0/24", "10.0.2.0/24"]
  private_subnet_cidrs = ["10.0.10.0/24", "10.0.20.0/24"]
  single_nat_gateway   = true  # Cost optimization for dev

  # EKS Cluster Configuration - Development specific
  cluster_version                      = "1.33"
  cluster_endpoint_public_access_cidrs = ["0.0.0.0/0"]  # Open access for dev
  cloudwatch_log_retention_in_days     = 3  # Short retention for dev

  # EKS Add-ons - Simplified for dev
  enable_ebs_csi_addon = false  # Disabled for simplicity

  # Node Groups Configuration - Development specific
  node_groups = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      desired_size   = 2
      max_size       = 3
      min_size       = 1
      disk_size      = 30
      labels = {
        role        = "general"
        environment = "dev"
      }
      taints = []
    }
  }

  # AWS Load Balancer Controller - Simplified for dev
  aws_load_balancer_controller_replica_count = 1

  # ArgoCD Configuration - Development specific
  argocd_enable_alb_ingress   = true
  argocd_enable_insecure_mode = true  # For ALB HTTP
  argocd_server_service_type  = "ClusterIP"  # Use ALB instead of LoadBalancer

  # Development-specific tags
  tags = {
    Environment = "dev"
    CostCenter  = "development"
    AutoShutdown = "true"  # For cost optimization
  }
}
