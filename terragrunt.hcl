# Root Terragrunt Configuration
# This file contains common configuration that will be inherited by all environments

# Configure Terragrunt to automatically store tfstate files in an S3 bucket
remote_state {
  backend = "s3"
  config = {
    encrypt        = true
    bucket         = "${get_env("TG_BUCKET_PREFIX", "eks-terraform-state")}-${get_aws_account_id()}"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = get_env("AWS_DEFAULT_REGION", "us-west-2")
    dynamodb_table = "${get_env("TG_BUCKET_PREFIX", "eks-terraform-state")}-locks"
    
    # S3 bucket versioning
    s3_bucket_tags = {
      Owner       = "terragrunt"
      Environment = "shared"
      ManagedBy   = "terragrunt"
    }
    
    # DynamoDB table tags
    dynamodb_table_tags = {
      Owner       = "terragrunt"
      Environment = "shared"
      ManagedBy   = "terragrunt"
    }
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

# Generate an AWS provider block
generate "provider" {
  path      = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents  = <<EOF
# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment = var.environment
      Project     = var.project_name
      ManagedBy   = "terragrunt"
      Owner       = var.owner
    }
  }
}

# Data source for EKS cluster
data "aws_eks_cluster" "cluster" {
  name       = module.eks_cluster.cluster_id
  depends_on = [module.eks_cluster]
}

data "aws_eks_cluster_auth" "cluster" {
  name       = module.eks_cluster.cluster_id
  depends_on = [module.eks_cluster]
}

# Configure Kubernetes provider
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

# Configure Helm provider
provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}
EOF
}

# Configure common inputs that are shared across all environments
inputs = {
  # These will be overridden by environment-specific values
}
