# Common configuration shared across all environments
# This file contains default values that can be overridden by environment-specific configurations

locals {
  # Common tags applied to all resources
  common_tags = {
    Project     = "eks-demo"
    ManagedBy   = "terragrunt"
    Owner       = "devops-team"
  }

  # Common VPC configuration
  vpc_config = {
    enable_nat_gateway = true
    enable_vpn_gateway = false
    enable_dns_hostnames = true
    enable_dns_support = true
  }

  # Common EKS configuration
  eks_config = {
    cluster_endpoint_private_access      = true
    cluster_endpoint_public_access       = true
    cluster_enabled_log_types            = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
    
    # EKS Add-ons
    enable_vpc_cni_addon     = true
    enable_coredns_addon     = true
    enable_kube_proxy_addon  = true
    enable_ebs_csi_addon     = true
  }

  # Common ArgoCD configuration
  argocd_config = {
    namespace               = "argocd"
    create_namespace        = true
    chart_version          = "8.2.7"
    argocd_version         = "v3.0.12"
    enable_irsa            = true
    server_service_type    = "LoadBalancer"
    load_balancer_scheme   = "internet-facing"
    enable_alb_ingress     = false
    enable_insecure_mode   = false
  }

  # Common AWS Load Balancer Controller configuration
  aws_load_balancer_controller_config = {
    chart_version = "1.8.1"
    image_tag     = "v2.8.1"
    replica_count = 2
  }
}

# Common inputs that will be merged with environment-specific inputs
inputs = {
  # Project information
  project_name = local.common_tags.Project
  owner        = local.common_tags.Owner

  # VPC configuration
  enable_nat_gateway   = local.vpc_config.enable_nat_gateway
  enable_vpn_gateway   = local.vpc_config.enable_vpn_gateway
  enable_dns_hostnames = local.vpc_config.enable_dns_hostnames
  enable_dns_support   = local.vpc_config.enable_dns_support

  # EKS configuration
  cluster_endpoint_private_access = local.eks_config.cluster_endpoint_private_access
  cluster_endpoint_public_access  = local.eks_config.cluster_endpoint_public_access
  cluster_enabled_log_types       = local.eks_config.cluster_enabled_log_types
  
  # EKS Add-ons
  enable_vpc_cni_addon    = local.eks_config.enable_vpc_cni_addon
  enable_coredns_addon    = local.eks_config.enable_coredns_addon
  enable_kube_proxy_addon = local.eks_config.enable_kube_proxy_addon
  enable_ebs_csi_addon    = local.eks_config.enable_ebs_csi_addon

  # ArgoCD configuration
  argocd_namespace            = local.argocd_config.namespace
  argocd_create_namespace     = local.argocd_config.create_namespace
  argocd_chart_version        = local.argocd_config.chart_version
  argocd_version              = local.argocd_config.argocd_version
  argocd_enable_irsa          = local.argocd_config.enable_irsa
  argocd_server_service_type  = local.argocd_config.server_service_type
  argocd_load_balancer_scheme = local.argocd_config.load_balancer_scheme
  argocd_enable_alb_ingress   = local.argocd_config.enable_alb_ingress
  argocd_enable_insecure_mode = local.argocd_config.enable_insecure_mode

  # AWS Load Balancer Controller configuration
  aws_load_balancer_controller_chart_version = local.aws_load_balancer_controller_config.chart_version
  aws_load_balancer_controller_image_tag     = local.aws_load_balancer_controller_config.image_tag
  aws_load_balancer_controller_replica_count = local.aws_load_balancer_controller_config.replica_count

  # Common tags
  tags = local.common_tags
}
