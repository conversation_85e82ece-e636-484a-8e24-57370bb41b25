# General Variables
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-west-2"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "eks-demo"
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "owner" {
  description = "Owner of the resources"
  type        = string
  default     = "devops-team"
}

# VPC Variables
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24"]
}

variable "enable_nat_gateway" {
  description = "Should be true to provision NAT Gateways for each of your private networks"
  type        = bool
  default     = true
}

variable "single_nat_gateway" {
  description = "Should be true to provision a single shared NAT Gateway across all of your private networks"
  type        = bool
  default     = true
}

variable "enable_vpn_gateway" {
  description = "Should be true to enable VPN gateway"
  type        = bool
  default     = false
}

variable "enable_dns_hostnames" {
  description = "Should be true to enable DNS hostnames in the VPC"
  type        = bool
  default     = true
}

variable "enable_dns_support" {
  description = "Should be true to enable DNS support in the VPC"
  type        = bool
  default     = true
}

# EKS Cluster Variables
variable "cluster_version" {
  description = "Kubernetes version to use for the EKS cluster"
  type        = string
  default     = "1.31"
}

variable "cluster_endpoint_private_access" {
  description = "Indicates whether or not the Amazon EKS private API server endpoint is enabled"
  type        = bool
  default     = true
}

variable "cluster_endpoint_public_access" {
  description = "Indicates whether or not the Amazon EKS public API server endpoint is enabled"
  type        = bool
  default     = true
}

variable "cluster_endpoint_public_access_cidrs" {
  description = "List of CIDR blocks which can access the Amazon EKS public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "cluster_enabled_log_types" {
  description = "A list of the desired control plane logging to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "cloudwatch_log_retention_in_days" {
  description = "Number of days to retain log events in CloudWatch"
  type        = number
  default     = 7
}

# EKS Add-ons Variables
variable "enable_vpc_cni_addon" {
  description = "Enable VPC CNI addon"
  type        = bool
  default     = true
}

variable "enable_coredns_addon" {
  description = "Enable CoreDNS addon"
  type        = bool
  default     = true
}

variable "enable_kube_proxy_addon" {
  description = "Enable kube-proxy addon"
  type        = bool
  default     = true
}

variable "enable_ebs_csi_addon" {
  description = "Enable EBS CSI addon"
  type        = bool
  default     = true
}

# Node Groups Variables
variable "node_groups" {
  description = "Map of EKS node group definitions to create"
  type = map(object({
    instance_types = list(string)
    capacity_type  = string
    desired_size   = number
    max_size       = number
    min_size       = number
    disk_size      = number
    labels         = map(string)
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
  }))
  default = {}
}

# AWS Load Balancer Controller Variables
variable "aws_load_balancer_controller_chart_version" {
  description = "Version of the AWS Load Balancer Controller Helm chart"
  type        = string
  default     = "1.8.1"
}

variable "aws_load_balancer_controller_image_tag" {
  description = "Docker image tag for AWS Load Balancer Controller"
  type        = string
  default     = "v2.8.1"
}

variable "aws_load_balancer_controller_replica_count" {
  description = "Number of replicas for AWS Load Balancer Controller"
  type        = number
  default     = 2
}

variable "aws_load_balancer_controller_service_account_name" {
  description = "Name of the Kubernetes service account for AWS Load Balancer Controller"
  type        = string
  default     = "aws-load-balancer-controller"
}

variable "aws_load_balancer_controller_release_name" {
  description = "Name of the Helm release for AWS Load Balancer Controller"
  type        = string
  default     = "aws-load-balancer-controller"
}

# ArgoCD Variables
variable "argocd_namespace" {
  description = "Kubernetes namespace for ArgoCD"
  type        = string
  default     = "argocd"
}

variable "argocd_create_namespace" {
  description = "Whether to create the ArgoCD namespace"
  type        = bool
  default     = true
}

variable "argocd_chart_version" {
  description = "Version of the ArgoCD Helm chart"
  type        = string
  default     = "8.2.7"
}

variable "argocd_version" {
  description = "Version of ArgoCD"
  type        = string
  default     = "v2.13.1"
}

variable "argocd_server_service_type" {
  description = "Service type for ArgoCD server"
  type        = string
  default     = "LoadBalancer"
}

variable "argocd_load_balancer_scheme" {
  description = "Load balancer scheme for ArgoCD"
  type        = string
  default     = "internet-facing"
}

variable "argocd_enable_irsa" {
  description = "Enable IAM Roles for Service Accounts for ArgoCD"
  type        = bool
  default     = true
}

variable "argocd_enable_alb_ingress" {
  description = "Enable ALB ingress for ArgoCD (alternative to LoadBalancer)"
  type        = bool
  default     = false
}

variable "argocd_alb_ingress_name" {
  description = "Name for the ArgoCD ALB ingress resource"
  type        = string
  default     = "argocd-alb-ingress"
}

variable "argocd_alb_load_balancer_name" {
  description = "Name for the ArgoCD ALB load balancer"
  type        = string
  default     = "argocd-alb"
}

variable "argocd_enable_insecure_mode" {
  description = "Enable ArgoCD server insecure mode (required for ALB HTTP)"
  type        = bool
  default     = false
}

# Tags
variable "tags" {
  description = "A map of tags to add to all resources"
  type        = map(string)
  default     = {}
}
