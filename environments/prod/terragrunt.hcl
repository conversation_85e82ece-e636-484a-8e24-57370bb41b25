# Production Environment Terragrunt Configuration

# Include the root terragrunt.hcl configuration
include "root" {
  path = find_in_parent_folders()
}

# Include common configuration
include "common" {
  path = "${dirname(find_in_parent_folders())}/common.hcl"
}

# Terraform source configuration
terraform {
  source = "${dirname(find_in_parent_folders())}"
}

# Environment-specific inputs
inputs = {
  # Environment identification
  environment = "prod"
  aws_region  = "us-west-2"

  # VPC Configuration - Production specific
  vpc_cidr             = "10.2.0.0/16"
  public_subnet_cidrs  = ["10.2.1.0/24", "10.2.2.0/24", "10.2.3.0/24"]
  private_subnet_cidrs = ["10.2.10.0/24", "10.2.20.0/24", "10.2.30.0/24"]
  single_nat_gateway   = false  # Multi-AZ for production

  # EKS Cluster Configuration - Production specific
  cluster_version                      = "1.31"
  cluster_endpoint_public_access       = false  # Private access only
  cluster_endpoint_public_access_cidrs = []
  cloudwatch_log_retention_in_days     = 30

  # Node Groups Configuration - Production specific
  node_groups = {
    general = {
      instance_types = ["m5.xlarge"]
      capacity_type  = "ON_DEMAND"
      desired_size   = 5
      max_size       = 10
      min_size       = 3
      disk_size      = 100
      labels = {
        role        = "general"
        environment = "prod"
      }
      taints = []
    }
    compute = {
      instance_types = ["c5.2xlarge"]
      capacity_type  = "ON_DEMAND"
      desired_size   = 3
      max_size       = 8
      min_size       = 2
      disk_size      = 100
      labels = {
        role        = "compute"
        environment = "prod"
      }
      taints = [
        {
          key    = "compute"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
    spot = {
      instance_types = ["m5.large", "m5.xlarge", "c5.large", "c5.xlarge"]
      capacity_type  = "SPOT"
      desired_size   = 2
      max_size       = 5
      min_size       = 0
      disk_size      = 100
      labels = {
        role        = "spot"
        environment = "prod"
      }
      taints = [
        {
          key    = "spot"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }

  # AWS Load Balancer Controller - Production specific
  aws_load_balancer_controller_replica_count = 3

  # ArgoCD Configuration - Production specific
  argocd_server_service_type  = "LoadBalancer"
  argocd_load_balancer_scheme = "internal"  # Internal access only

  # Production-specific tags
  tags = {
    Environment = "prod"
    CostCenter  = "production"
    Backup      = "daily"
    Monitoring  = "critical"
    Compliance  = "required"
  }
}
