output "node_group_arn" {
  description = "Amazon Resource Name (ARN) of the EKS Node Group"
  value       = aws_eks_node_group.main.arn
}

output "node_group_id" {
  description = "EKS cluster name and EKS Node Group name separated by a colon"
  value       = aws_eks_node_group.main.id
}

output "node_group_status" {
  description = "Status of the EKS Node Group"
  value       = aws_eks_node_group.main.status
}

output "node_group_capacity_type" {
  description = "Type of capacity associated with the EKS Node Group"
  value       = aws_eks_node_group.main.capacity_type
}

output "node_group_instance_types" {
  description = "List of instance types associated with the EKS Node Group"
  value       = aws_eks_node_group.main.instance_types
}

output "node_group_ami_type" {
  description = "Type of Amazon Machine Image (AMI) associated with the EKS Node Group"
  value       = aws_eks_node_group.main.ami_type
}

output "node_group_version" {
  description = "Kubernetes version of the EKS Node Group"
  value       = aws_eks_node_group.main.version
}

output "node_group_remote_access_security_group_id" {
  description = "Identifier of the remote access EC2 Security Group"
  value       = try(aws_eks_node_group.main.remote_access[0].source_security_group_ids, null)
}

output "node_group_resources" {
  description = "List of objects containing information about underlying resources"
  value       = aws_eks_node_group.main.resources
}

output "node_group_iam_role_name" {
  description = "IAM role name associated with EKS node group"
  value       = aws_iam_role.node_group.name
}

output "node_group_iam_role_arn" {
  description = "IAM role ARN associated with EKS node group"
  value       = aws_iam_role.node_group.arn
}

output "launch_template_id" {
  description = "The ID of the launch template"
  value       = try(aws_launch_template.node_group[0].id, null)
}

output "launch_template_arn" {
  description = "The ARN of the launch template"
  value       = try(aws_launch_template.node_group[0].arn, null)
}

output "launch_template_latest_version" {
  description = "The latest version of the launch template"
  value       = try(aws_launch_template.node_group[0].latest_version, null)
}
