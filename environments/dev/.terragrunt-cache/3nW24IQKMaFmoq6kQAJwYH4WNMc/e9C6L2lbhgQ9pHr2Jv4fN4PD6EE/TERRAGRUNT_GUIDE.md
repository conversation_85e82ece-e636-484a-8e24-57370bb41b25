# Terragrunt Deployment Guide

This guide provides detailed instructions for deploying and managing the EKS infrastructure using Terragrunt.

## 📋 Prerequisites

### Required Tools

1. **AWS CLI** (v2.x recommended)
   ```bash
   # Install AWS CLI
   curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
   unzip awscliv2.zip
   sudo ./aws/install
   
   # Configure AWS credentials
   aws configure
   ```

2. **Terraform** (>= 1.0)
   ```bash
   # Install Terraform
   wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
   unzip terraform_1.6.0_linux_amd64.zip
   sudo mv terraform /usr/local/bin/
   ```

3. **Terragrunt** (>= 0.50.0)
   ```bash
   # Install Terragrunt
   wget https://github.com/gruntwork-io/terragrunt/releases/download/v0.53.0/terragrunt_linux_amd64
   chmod +x terragrunt_linux_amd64
   sudo mv terragrunt_linux_amd64 /usr/local/bin/terragrunt
   ```

4. **kubectl**
   ```bash
   # Install kubectl
   curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
   chmod +x kubectl
   sudo mv kubectl /usr/local/bin/
   ```

5. **Helm**
   ```bash
   # Install Helm
   curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
   ```

### AWS Permissions

Ensure your AWS user/role has the following permissions:

- **EC2**: Full access for VPC, subnets, security groups, NAT gateways
- **EKS**: Full access for cluster and node group management
- **IAM**: Full access for roles and policies (IRSA)
- **CloudWatch**: Log group management
- **KMS**: Key management for encryption
- **S3**: Bucket management for Terraform state
- **DynamoDB**: Table management for state locking

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd eks-terraform
```

### 2. Configure Environment Variables (Optional)

```bash
# Customize S3 bucket prefix (optional)
export TG_BUCKET_PREFIX="my-company-terraform-state"

# Set AWS region (optional, defaults to us-west-2)
export AWS_DEFAULT_REGION="us-west-2"
```

### 3. Deploy Development Environment

```bash
# Initialize Terragrunt
make dev-init

# Review the plan
make dev-plan

# Apply the changes
make dev-apply
```

### 4. Configure kubectl

```bash
# Configure kubectl to access the cluster
make kubectl-config ENV=dev
```

### 5. Access ArgoCD

```bash
# Get ArgoCD admin password
make argocd-password ENV=dev

# Port forward to ArgoCD (for development with ALB)
make argocd-port-forward
```

## 🏗️ Architecture Overview

### Terragrunt Structure

```
├── terragrunt.hcl          # Root configuration (backend, providers)
├── common.hcl              # Common settings across environments
├── main.tf                 # Root Terraform configuration
├── variables.tf            # Variable definitions
└── environments/
    ├── dev/
    │   └── terragrunt.hcl  # Dev-specific overrides
    ├── staging/
    │   └── terragrunt.hcl  # Staging-specific overrides
    └── prod/
        └── terragrunt.hcl  # Prod-specific overrides
```

### Configuration Hierarchy

1. **Root** (`terragrunt.hcl`): Backend and provider configuration
2. **Common** (`common.hcl`): Default values shared across environments
3. **Environment** (`environments/{env}/terragrunt.hcl`): Environment-specific overrides

## 🌍 Environment Management

### Development Environment

**Characteristics:**
- Single NAT Gateway (cost optimization)
- t3.medium instances
- Open public access
- ALB ingress for ArgoCD
- 3-day log retention

```bash
# Deploy development
make dev-init
make dev-plan
make dev-apply

# Access cluster
make kubectl-config ENV=dev
make argocd-password ENV=dev
```

### Staging Environment

**Characteristics:**
- Multi-AZ NAT Gateways
- t3.large instances + spot instances
- Restricted public access
- Internal LoadBalancer for ArgoCD
- 14-day log retention

```bash
# Deploy staging
make staging-init
make staging-plan
make staging-apply

# Access cluster
make kubectl-config ENV=staging
```

### Production Environment

**Characteristics:**
- Multi-AZ NAT Gateways
- m5.xlarge + c5.2xlarge instances
- Private API endpoint only
- Internal LoadBalancer for ArgoCD
- 30-day log retention

```bash
# Deploy production
make prod-init
make prod-plan
make prod-apply

# Access cluster (requires VPN/bastion)
make kubectl-config ENV=prod
```

## 🔧 Customization

### Modifying Common Settings

Edit `common.hcl` to change settings across all environments:

```hcl
locals {
  # Update ArgoCD versions
  argocd_config = {
    chart_version = "8.3.0"  # New chart version
    argocd_version = "v2.14.0"  # New ArgoCD version
  }
  
  # Update EKS settings
  eks_config = {
    cluster_version = "1.32"  # New Kubernetes version
  }
}
```

### Environment-Specific Overrides

Edit `environments/{env}/terragrunt.hcl` for environment-specific changes:

```hcl
inputs = {
  # Override cluster version for this environment
  cluster_version = "1.31"
  
  # Add custom node groups
  node_groups = {
    gpu = {
      instance_types = ["g4dn.xlarge"]
      capacity_type  = "ON_DEMAND"
      desired_size   = 1
      max_size       = 3
      min_size       = 0
      disk_size      = 100
      labels = {
        role = "gpu"
        "nvidia.com/gpu" = "true"
      }
      taints = [
        {
          key    = "nvidia.com/gpu"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }
}
```

## 🛠️ Troubleshooting

### Common Issues

1. **Terragrunt not found**
   ```bash
   # Install Terragrunt
   wget https://github.com/gruntwork-io/terragrunt/releases/latest/download/terragrunt_linux_amd64
   chmod +x terragrunt_linux_amd64
   sudo mv terragrunt_linux_amd64 /usr/local/bin/terragrunt
   ```

2. **AWS permissions error**
   ```bash
   # Check AWS credentials
   aws sts get-caller-identity
   
   # Verify permissions
   aws iam get-user
   ```

3. **State lock error**
   ```bash
   # Force unlock (use with caution)
   cd environments/dev
   terragrunt force-unlock <lock-id>
   ```

4. **Backend bucket doesn't exist**
   ```bash
   # Terragrunt will create it automatically, but you can create manually:
   aws s3 mb s3://eks-terraform-state-$(aws sts get-caller-identity --query Account --output text)
   ```

### Debugging

```bash
# Enable debug logging
export TF_LOG=DEBUG
export TERRAGRUNT_LOG_LEVEL=debug

# Run with verbose output
cd environments/dev
terragrunt plan --terragrunt-log-level debug
```

## 🧹 Maintenance

### Updating Versions

1. **Update ArgoCD version** in `common.hcl`
2. **Update Terraform providers** in `main.tf`
3. **Update Kubernetes version** per environment

### State Management

```bash
# View state
terragrunt state list

# Import existing resources
terragrunt import aws_instance.example i-1234567890abcdef0

# Remove resources from state
terragrunt state rm aws_instance.example
```

### Cleanup

```bash
# Clean specific environment
make clean ENV=dev

# Destroy environment
make destroy ENV=dev

# Clean all Terragrunt cache
find . -name ".terragrunt-cache" -type d -exec rm -rf {} +
```

## 📚 Additional Resources

- [Terragrunt Documentation](https://terragrunt.gruntwork.io/docs/)
- [AWS EKS Best Practices](https://aws.github.io/aws-eks-best-practices/)
- [ArgoCD Documentation](https://argo-cd.readthedocs.io/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)
