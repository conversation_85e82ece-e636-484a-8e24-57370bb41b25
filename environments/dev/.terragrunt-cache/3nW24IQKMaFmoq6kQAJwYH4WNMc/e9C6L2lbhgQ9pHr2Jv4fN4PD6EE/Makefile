# EKS Terragrunt Multi-Environment Makefile
# This Makefile provides convenient commands for managing EKS infrastructure across environments using Terragrunt

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m # No Color

# Default environment
ENV ?= dev

# Validate environment
VALID_ENVS := dev staging prod
ifeq ($(filter $(ENV),$(VALID_ENVS)),)
$(error Invalid environment: $(ENV). Valid environments are: $(VALID_ENVS))
endif

# Environment directory
ENV_DIR := environments/$(ENV)

.PHONY: help check-env check-terragrunt init plan apply destroy clean validate fmt lint security-scan cost-estimate status logs argocd-password argocd-port-forward

help: ## Show this help message
	@echo "$(BLUE)EKS Terragrunt Multi-Environment Management$(NC)"
	@echo ""
	@echo "$(YELLOW)Usage:$(NC)"
	@echo "  make <target> ENV=<environment>"
	@echo ""
	@echo "$(YELLOW)Environments:$(NC)"
	@echo "  dev      - Development environment"
	@echo "  staging  - Staging environment"
	@echo "  prod     - Production environment"
	@echo ""
	@echo "$(YELLOW)Targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

check-env: ## Validate environment parameter
	@echo "$(BLUE)Using environment: $(ENV)$(NC)"
	@if [ ! -d "$(ENV_DIR)" ]; then \
		echo "$(RED)Error: Environment directory $(ENV_DIR) does not exist$(NC)"; \
		exit 1; \
	fi

check-terragrunt: ## Check if Terragrunt is installed
	@if ! command -v terragrunt >/dev/null 2>&1; then \
		echo "$(RED)Error: Terragrunt is not installed$(NC)"; \
		echo "$(YELLOW)Install Terragrunt: https://terragrunt.gruntwork.io/docs/getting-started/install/$(NC)"; \
		exit 1; \
	fi

init: check-env check-terragrunt ## Initialize Terragrunt for the specified environment
	@echo "$(BLUE)Initializing Terragrunt for $(ENV) environment...$(NC)"
	cd $(ENV_DIR) && terragrunt init

plan: check-env check-terragrunt ## Plan Terragrunt changes for the specified environment
	@echo "$(BLUE)Planning Terragrunt changes for $(ENV) environment...$(NC)"
	cd $(ENV_DIR) && terragrunt plan

apply: check-env check-terragrunt ## Apply Terragrunt changes for the specified environment
	@echo "$(BLUE)Applying Terragrunt changes for $(ENV) environment...$(NC)"
	@echo "$(YELLOW)Warning: This will make changes to your infrastructure!$(NC)"
	@read -p "Are you sure you want to continue? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		cd $(ENV_DIR) && terragrunt apply; \
	else \
		echo "$(YELLOW)Operation cancelled.$(NC)"; \
	fi

destroy: check-env check-terragrunt ## Destroy Terragrunt infrastructure for the specified environment
	@echo "$(RED)Destroying Terragrunt infrastructure for $(ENV) environment...$(NC)"
	@echo "$(RED)WARNING: This will destroy all infrastructure in the $(ENV) environment!$(NC)"
	@read -p "Are you sure you want to continue? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		cd $(ENV_DIR) && terragrunt destroy; \
	else \
		echo "$(YELLOW)Operation cancelled.$(NC)"; \
	fi

clean: check-env ## Clean Terragrunt cache and lock files
	@echo "$(BLUE)Cleaning Terragrunt cache for $(ENV) environment...$(NC)"
	cd $(ENV_DIR) && rm -rf .terraform .terraform.lock.hcl .terragrunt-cache backend.tf provider.tf

validate: check-env check-terragrunt ## Validate Terragrunt configuration
	@echo "$(BLUE)Validating Terragrunt configuration for $(ENV) environment...$(NC)"
	cd $(ENV_DIR) && terragrunt validate

fmt: ## Format Terraform files
	@echo "$(BLUE)Formatting Terraform files...$(NC)"
	terraform fmt -recursive .

lint: ## Run terraform lint (tflint)
	@echo "$(BLUE)Running terraform lint...$(NC)"
	@if command -v tflint >/dev/null 2>&1; then \
		tflint --recursive; \
	else \
		echo "$(YELLOW)tflint not installed. Install with: brew install tflint$(NC)"; \
	fi

security-scan: ## Run security scan (checkov)
	@echo "$(BLUE)Running security scan...$(NC)"
	@if command -v checkov >/dev/null 2>&1; then \
		checkov -d . --framework terraform; \
	else \
		echo "$(YELLOW)checkov not installed. Install with: pip install checkov$(NC)"; \
	fi

cost-estimate: check-env check-terragrunt ## Estimate costs (infracost)
	@echo "$(BLUE)Estimating costs for $(ENV) environment...$(NC)"
	@if command -v infracost >/dev/null 2>&1; then \
		cd $(ENV_DIR) && terragrunt run-all plan --terragrunt-non-interactive && infracost breakdown --path .; \
	else \
		echo "$(YELLOW)infracost not installed. Install from: https://www.infracost.io/docs/$(NC)"; \
	fi

status: check-env check-terragrunt ## Show Terragrunt state status
	@echo "$(BLUE)Terragrunt state status for $(ENV) environment...$(NC)"
	cd $(ENV_DIR) && terragrunt show -json | jq -r '.values.root_module.resources[] | select(.type != null) | "\(.type).\(.name)"' | sort

logs: check-env ## Show EKS cluster logs
	@echo "$(BLUE)Showing EKS cluster logs for $(ENV) environment...$(NC)"
	@CLUSTER_NAME=$$(cd $(ENV_DIR) && terragrunt output -raw cluster_id 2>/dev/null || echo "eks-demo-$(ENV)"); \
	aws logs describe-log-groups --log-group-name-prefix "/aws/eks/$$CLUSTER_NAME" --query 'logGroups[].logGroupName' --output table

argocd-password: check-env ## Get ArgoCD admin password
	@echo "$(BLUE)Getting ArgoCD admin password for $(ENV) environment...$(NC)"
	@kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d
	@echo ""

argocd-port-forward: ## Port forward to ArgoCD server
	@echo "$(BLUE)Port forwarding to ArgoCD server...$(NC)"
	@echo "$(YELLOW)ArgoCD will be available at https://localhost:8080$(NC)"
	@kubectl port-forward svc/argocd-server -n argocd 8080:443

# Terragrunt-specific commands
run-all-init: check-terragrunt ## Initialize all environments
	@echo "$(BLUE)Initializing all environments with Terragrunt...$(NC)"
	terragrunt run-all init

run-all-plan: check-terragrunt ## Plan all environments
	@echo "$(BLUE)Planning all environments with Terragrunt...$(NC)"
	terragrunt run-all plan

run-all-apply: check-terragrunt ## Apply all environments
	@echo "$(BLUE)Applying all environments with Terragrunt...$(NC)"
	@echo "$(RED)WARNING: This will apply changes to ALL environments!$(NC)"
	@read -p "Are you sure you want to continue? [y/N] " -n 1 -r; \
	echo ""; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		terragrunt run-all apply; \
	else \
		echo "$(YELLOW)Operation cancelled.$(NC)"; \
	fi

# Environment-specific shortcuts
dev-init: ## Initialize development environment
	@$(MAKE) init ENV=dev

dev-plan: ## Plan development environment
	@$(MAKE) plan ENV=dev

dev-apply: ## Apply development environment
	@$(MAKE) apply ENV=dev

staging-init: ## Initialize staging environment
	@$(MAKE) init ENV=staging

staging-plan: ## Plan staging environment
	@$(MAKE) plan ENV=staging

staging-apply: ## Apply staging environment
	@$(MAKE) apply ENV=staging

prod-init: ## Initialize production environment
	@$(MAKE) init ENV=prod

prod-plan: ## Plan production environment
	@$(MAKE) plan ENV=prod

prod-apply: ## Apply production environment
	@$(MAKE) apply ENV=prod
