#!/bin/bash

# EKS Deployment Script
# Usage: ./scripts/deploy.sh <environment> [plan|apply|destroy]

set -e

ENVIRONMENT=$1
ACTION=${2:-plan}
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validate inputs
if [[ -z "$ENVIRONMENT" ]]; then
    print_error "Environment is required. Usage: $0 <environment> [plan|apply|destroy]"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

if [[ ! "$ACTION" =~ ^(plan|apply|destroy)$ ]]; then
    print_error "Invalid action: $ACTION"
    print_error "Available actions: plan, apply, destroy"
    exit 1
fi

ENV_DIR="$PROJECT_ROOT/environments/$ENVIRONMENT"

if [[ ! -d "$ENV_DIR" ]]; then
    print_error "Environment directory not found: $ENV_DIR"
    exit 1
fi

print_status "Deploying $ENVIRONMENT environment with action: $ACTION"

# Change to environment directory
cd "$ENV_DIR"

# Check if terraform.tfvars exists
if [[ ! -f "terraform.tfvars" ]]; then
    print_warning "terraform.tfvars not found. Creating from example..."
    if [[ -f "terraform.tfvars.example" ]]; then
        cp terraform.tfvars.example terraform.tfvars
        print_warning "Please edit terraform.tfvars with your specific values before proceeding."
        exit 1
    else
        print_error "terraform.tfvars.example not found"
        exit 1
    fi
fi

# Initialize Terraform
print_status "Initializing Terraform..."
terraform init

# Validate configuration
print_status "Validating Terraform configuration..."
terraform validate

# Execute the requested action
case $ACTION in
    plan)
        print_status "Running Terraform plan..."
        terraform plan -out="tfplan-$ENVIRONMENT"
        print_success "Plan completed successfully!"
        print_status "To apply this plan, run: terraform apply tfplan-$ENVIRONMENT"
        ;;
    apply)
        print_status "Running Terraform apply..."
        if [[ -f "tfplan-$ENVIRONMENT" ]]; then
            terraform apply "tfplan-$ENVIRONMENT"
        else
            terraform apply
        fi
        print_success "Apply completed successfully!"
        
        # Get cluster info
        CLUSTER_NAME=$(terraform output -raw cluster_id 2>/dev/null || echo "")
        AWS_REGION=$(terraform output -raw aws_region 2>/dev/null || grep 'aws_region.*=' terraform.tfvars | cut -d'"' -f2 || echo "us-west-2")
        
        if [[ -n "$CLUSTER_NAME" ]]; then
            print_status "Configuring kubectl..."
            aws eks update-kubeconfig --region "$AWS_REGION" --name "$CLUSTER_NAME"
            print_success "kubectl configured for cluster: $CLUSTER_NAME"
            
            print_status "Getting ArgoCD admin password..."
            ARGOCD_PASSWORD=$(kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" 2>/dev/null | base64 -d 2>/dev/null || echo "Not available yet")
            
            print_success "Deployment completed!"
            echo ""
            echo "=== Cluster Information ==="
            echo "Cluster Name: $CLUSTER_NAME"
            echo "Region: $AWS_REGION"
            echo "ArgoCD Username: admin"
            echo "ArgoCD Password: $ARGOCD_PASSWORD"
            echo ""
            echo "=== Next Steps ==="
            echo "1. Configure kubectl: aws eks update-kubeconfig --region $AWS_REGION --name $CLUSTER_NAME"
            echo "2. Access ArgoCD: kubectl port-forward svc/argocd-server -n argocd 8080:443"
            echo "3. Login to ArgoCD at https://localhost:8080 with admin/$ARGOCD_PASSWORD"
        fi
        ;;
    destroy)
        print_warning "This will destroy all resources in the $ENVIRONMENT environment!"
        read -p "Are you sure you want to continue? (yes/no): " -r
        if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            print_status "Running Terraform destroy..."
            terraform destroy
            print_success "Destroy completed successfully!"
        else
            print_status "Destroy cancelled."
        fi
        ;;
esac

print_success "Script completed successfully!"
