# EKS Terraform Deployment Guide

This guide provides step-by-step instructions for deploying the EKS infrastructure.

## 🚀 Quick Deployment

### Option 1: Using Make (Recommended)

```bash
# Setup backend (one-time)
make setup-backend BUCKET_NAME=my-terraform-state-bucket

# Deploy development environment
make deploy-dev

# Configure kubectl
make kubectl-config ENV=dev

# Get ArgoCD password
make argocd-password ENV=dev

# Port forward to ArgoCD
make argocd-port-forward
```

### Option 2: Using Scripts

```bash
# Setup backend (one-time)
./scripts/setup-backend.sh my-terraform-state-bucket us-west-2

# Deploy environment
./scripts/deploy.sh dev apply
```

### Option 3: Manual Deployment

```bash
# Navigate to environment
cd environments/dev

# Copy and edit variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Initialize and deploy
terraform init
terraform plan
terraform apply
```

## 📋 Pre-Deployment Checklist

### AWS Prerequisites

- [ ] AWS CLI installed and configured
- [ ] Appropriate IAM permissions (see README.md)
- [ ] S3 bucket for Terraform state
- [ ] DynamoDB table for state locking

### Tools Required

- [ ] Terraform >= 1.0
- [ ] kubectl
- [ ] Helm (optional, for manual ArgoCD management)
- [ ] jq (optional, for JSON parsing)

### Configuration Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd eks-terraform
   ```

2. **Setup Terraform backend**
   ```bash
   # Create S3 bucket and DynamoDB table
   ./scripts/setup-backend.sh your-terraform-state-bucket us-west-2
   ```

3. **Configure environment variables**
   ```bash
   cd environments/dev
   cp terraform.tfvars.example terraform.tfvars
   ```

4. **Edit terraform.tfvars**
   - Update `aws_region` to your preferred region
   - Set `project_name` to your project name
   - Configure `vpc_cidr` if needed
   - Adjust node group configurations
   - Set ArgoCD preferences

5. **Update backend configuration**
   Edit `main.tf` in your environment directory:
   ```hcl
   backend "s3" {
     bucket         = "your-terraform-state-bucket"
     key            = "eks/dev/terraform.tfstate"
     region         = "us-west-2"
     dynamodb_table = "terraform-state-lock"
     encrypt        = true
   }
   ```

## 🌍 Environment-Specific Deployment

### Development Environment

```bash
cd environments/dev
terraform init
terraform plan -out=tfplan-dev
terraform apply tfplan-dev
```

**Characteristics:**
- Smaller instance types (t3.medium)
- Single NAT gateway (cost optimization)
- Public access allowed
- 7-day log retention

### Staging Environment

```bash
cd environments/staging
terraform init
terraform plan -out=tfplan-staging
terraform apply tfplan-staging
```

**Characteristics:**
- Medium instance types (t3.large)
- Multi-AZ NAT gateways
- Restricted public access
- 14-day log retention

### Production Environment

```bash
cd environments/prod
terraform init
terraform plan -out=tfplan-prod
terraform apply tfplan-prod
```

**Characteristics:**
- Large instance types (m5.xlarge, c5.2xlarge)
- Multi-AZ NAT gateways
- Private network access only
- 30-day log retention
- Internal load balancers

## 🔧 Post-Deployment Configuration

### 1. Configure kubectl

```bash
aws eks update-kubeconfig --region us-west-2 --name eks-demo-dev
```

### 2. Verify cluster access

```bash
kubectl get nodes
kubectl get pods -A
```

### 3. Access ArgoCD

Get the admin password:
```bash
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d
```

Port forward to ArgoCD:
```bash
kubectl port-forward svc/argocd-server -n argocd 8080:443
```

Access ArgoCD at https://localhost:8080
- Username: `admin`
- Password: (from the command above)

### 4. Configure ArgoCD (Optional)

Create your first application:
```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: my-app
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/your-org/your-app
    targetRevision: HEAD
    path: k8s
  destination:
    server: https://kubernetes.default.svc
    namespace: default
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

## 🔍 Troubleshooting

### Common Issues

1. **Backend initialization fails**
   - Ensure S3 bucket exists and you have permissions
   - Check DynamoDB table exists
   - Verify AWS credentials

2. **EKS cluster creation fails**
   - Check IAM permissions
   - Verify VPC/subnet configuration
   - Ensure region has EKS service available

3. **Node groups fail to join**
   - Check security group rules
   - Verify IAM roles and policies
   - Check subnet routing

4. **ArgoCD not accessible**
   - Verify pods are running: `kubectl get pods -n argocd`
   - Check service: `kubectl get svc -n argocd`
   - Ensure port forwarding is active

### Useful Commands

```bash
# Check cluster status
kubectl get nodes
kubectl cluster-info

# Check ArgoCD status
kubectl get pods -n argocd
kubectl get svc -n argocd

# View Terraform state
terraform show
terraform state list

# Debug networking
kubectl get svc -A
kubectl get ingress -A
```

## 🧹 Cleanup

### Destroy Environment

```bash
# Using Make
make destroy ENV=dev

# Using script
./scripts/deploy.sh dev destroy

# Manual
cd environments/dev
terraform destroy
```

### Complete Cleanup

```bash
# Clean all temporary files
make clean

# Remove state files (be careful!)
find . -name "terraform.tfstate*" -delete
```

## 📊 Monitoring and Maintenance

### Regular Tasks

1. **Update Kubernetes version**
   - Update `cluster_version` in terraform.tfvars
   - Plan and apply changes
   - Update node groups if needed

2. **Scale node groups**
   - Modify `desired_size`, `min_size`, `max_size` in terraform.tfvars
   - Apply changes

3. **Update ArgoCD**
   - Update `argocd_version` or `argocd_chart_version`
   - Apply changes

### Monitoring

- CloudWatch logs for EKS control plane
- Node group auto-scaling metrics
- ArgoCD application sync status
- AWS Cost Explorer for cost monitoring

## 🔐 Security Best Practices

1. **Restrict cluster access**
   - Use private endpoints in production
   - Limit `cluster_endpoint_public_access_cidrs`

2. **Use IRSA**
   - Enable `argocd_enable_irsa`
   - Create service-specific IAM roles

3. **Network security**
   - Use private subnets for worker nodes
   - Configure security groups properly
   - Enable VPC flow logs

4. **Secrets management**
   - Use AWS Secrets Manager or Parameter Store
   - Rotate ArgoCD admin password
   - Use Kubernetes secrets for sensitive data

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Terraform and kubectl logs
3. Consult AWS EKS documentation
4. Open an issue in the repository
