# Development Environment Configuration

# General Settings
aws_region   = "us-west-2"
project_name = "eks-demo"
environment  = "dev"
owner        = "devops-team"

# VPC Configuration
vpc_cidr             = "10.0.0.0/16"
public_subnet_cidrs  = ["10.0.1.0/24", "10.0.2.0/24"]
private_subnet_cidrs = ["10.0.10.0/24", "10.0.20.0/24"]
enable_nat_gateway   = true
single_nat_gateway   = true  # Cost optimization for dev

# EKS Cluster Configuration
cluster_version                      = "1.28"
cluster_endpoint_private_access      = true
cluster_endpoint_public_access       = true
cluster_endpoint_public_access_cidrs = ["0.0.0.0/0"]  # Restrict this in production
cluster_enabled_log_types            = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
cloudwatch_log_retention_in_days     = 7

# EKS Add-ons
enable_vpc_cni_addon    = true
enable_coredns_addon    = true
enable_kube_proxy_addon = true
enable_ebs_csi_addon    = true

# Node Groups Configuration
node_groups = {
  general = {
    instance_types = ["t3.medium"]
    capacity_type  = "ON_DEMAND"
    desired_size   = 2
    max_size       = 4
    min_size       = 1
    disk_size      = 50
    labels = {
      role        = "general"
      environment = "dev"
    }
    taints = []
  }
  
  # Uncomment to add a spot instance node group for cost optimization
  # spot = {
  #   instance_types = ["t3.medium", "t3.large"]
  #   capacity_type  = "SPOT"
  #   desired_size   = 1
  #   max_size       = 3
  #   min_size       = 0
  #   disk_size      = 50
  #   labels = {
  #     role        = "spot"
  #     environment = "dev"
  #   }
  #   taints = [
  #     {
  #       key    = "spot"
  #       value  = "true"
  #       effect = "NO_SCHEDULE"
  #     }
  #   ]
  # }
}

# ArgoCD Configuration
argocd_namespace             = "argocd"
argocd_create_namespace      = true
argocd_chart_version         = "5.51.6"
argocd_version               = "v2.9.3"
argocd_server_service_type   = "LoadBalancer"
argocd_load_balancer_scheme  = "internet-facing"
argocd_enable_irsa           = true
