# Generated by Terragrunt. Sig: nIlQXj57tbuaRZEa
# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment = var.environment
      Project     = var.project_name
      ManagedBy   = "terragrunt"
      Owner       = var.owner
    }
  }
}

# Data source for EKS cluster
data "aws_eks_cluster" "cluster" {
  name       = module.eks_cluster.cluster_id
  depends_on = [module.eks_cluster]
}

data "aws_eks_cluster_auth" "cluster" {
  name       = module.eks_cluster.cluster_id
  depends_on = [module.eks_cluster]
}

# Configure Kubernetes provider
provider "kubernetes" {
  host                   = data.aws_eks_cluster.cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.cluster.token
}

# Configure Helm provider
provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.cluster.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
    token                  = data.aws_eks_cluster_auth.cluster.token
  }
}
